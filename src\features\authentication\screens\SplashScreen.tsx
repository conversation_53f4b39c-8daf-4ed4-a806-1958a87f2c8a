// ... existing code ...
"use client"

import { useEffect, useRef, useState } from "react"
import { View, StyleSheet, StatusBar, SafeAreaView, Animated, Dimensions } from "react-native"
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../../../navigation/navigation"
import { useTheme } from "../../../shared/components/layout/ThemeContext"
import { logger } from '../../../infrastructure/monitoring/productionLogger';
import { navigationHandler } from '../../../navigation/handlers/navigationHandler';

type Props = NativeStackScreenProps<RootStackParamList, "Splash">

const { width, height } = Dimensions.get("window")

const SplashScreen = ({ }: Props) => {
  const { theme, isDark } = useTheme()

  // Simplified animation refs for minimalist design
  const logoFadeAnim = useRef(new Animated.Value(0)).current
  const logoSlideAnim = useRef(new Animated.Value(-30)).current
  const loadingFadeAnim = useRef(new Animated.Value(0)).current
  const loadingSlideAnim = useRef(new Animated.Value(20)).current
  const progressAnim = useRef(new Animated.Value(0)).current
  const pulseAnim = useRef(new Animated.Value(1)).current

  const [welcomeMessage, setWelcomeMessage] = useState<string | null>(null)

  useEffect(() => {
    // Clean, subtle entrance animations
    const startAnimations = () => {
      // Logo entrance - gentle fade in and slide down
      Animated.parallel([
        Animated.timing(logoFadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(logoSlideAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start()

      // Loading indicator entrance - delayed fade in and slide up
      setTimeout(() => {
        Animated.parallel([
          Animated.timing(loadingFadeAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(loadingSlideAnim, {
            toValue: 0,
            duration: 800,
            useNativeDriver: true,
          }),
        ]).start()
      }, 500)

      // Subtle logo pulse animation
      setTimeout(() => {
        Animated.loop(
          Animated.sequence([
            Animated.timing(pulseAnim, {
              toValue: 1.03,
              duration: 2500,
              useNativeDriver: true,
            }),
            Animated.timing(pulseAnim, {
              toValue: 1,
              duration: 2500,
              useNativeDriver: true,
            }),
          ])
        ).start()
      }, 1000)

      // Progress animation
      setTimeout(() => {
        Animated.timing(progressAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: false,
        }).start()
      }, 800)
    }

    startAnimations()

    // Check for existing authentication and navigate accordingly
    const checkAuthAndNavigate = async () => {
      try {
        logger.info('Checking for existing authentication', null, 'splash');

        // Import AsyncStorage dynamically
        const AsyncStorage = await import('@react-native-async-storage/async-storage')

        // FIRST: Check if user was in the middle of email verification
        const emailVerificationState = await AsyncStorage.default.getItem('emailVerificationState')
        if (emailVerificationState) {
          try {
            const verificationData = JSON.parse(emailVerificationState)
            const timeDiff = Date.now() - verificationData.timestamp

            // If verification state is less than 30 minutes old, return to verification screen
            if (timeDiff < 30 * 60 * 1000) { // 30 minutes
              logger.info('📱 [SPLASH] Found active email verification state, returning to EmailVerification', {
                email: verificationData.email,
                ageMinutes: Math.round(timeDiff / 60000)
              }, 'splash');

              navigationHandler.navigateToEmailVerification(verificationData.email);
              return;
            } else {
              // State is too old, clear it
              await AsyncStorage.default.removeItem('emailVerificationState');
              logger.info('📱 [SPLASH] Email verification state expired, cleared', null, 'splash');
            }
          } catch (error) {
            logger.error('Failed to parse email verification state', error, 'splash');
            await AsyncStorage.default.removeItem('emailVerificationState');
          }
        }

        // Check for stored access token
        const accessToken = await AsyncStorage.default.getItem('accessToken')

        if (accessToken && accessToken !== 'undefined') {
          logger.info('Found stored access token, validating', null, 'splash');

          // Import API service to validate token
          const { default: ApiService } = await import('../../../infrastructure/api/apiService')

          try {
            // Try to get setup status to validate token and get user info
            const response = await ApiService.get('/setup/status')

            if (response.data.status === 'success') {
              logger.info('Token is valid, user is authenticated', null, 'splash');

              // Create user data from setup response
              const userData = {
                ...response.data.data.user,
                authMethod: 'token',
                isNewUser: false,
                avatar: response.data.data.user?.picture || response.data.data.user?.avatar,
                hasPinSetup: response.data.data.setupStatus?.hasPinSetup,
                hasBiometricSetup: response.data.data.setupStatus?.hasBiometricSetup,
                hasProfileSetup: response.data.data.setupStatus?.hasProfileSetup,
                setupComplete: response.data.data.setupStatus?.setupComplete,
              }

              logger.info('User data from token validation', { 
                hasId: !!userData.id, 
                hasEmail: !!userData.email,
                setupComplete: userData.setupComplete 
              }, 'splash');

              // Check if user has completed profile setup
              logger.info('🔍 [SPLASH] Setup status check', {
                hasProfileSetup: response.data.data.setupStatus?.hasProfileSetup,
                hasPinSetup: response.data.data.setupStatus?.hasPinSetup,
                hasBiometricSetup: response.data.data.setupStatus?.hasBiometricSetup,
                setupComplete: response.data.data.setupStatus?.setupComplete,
                isEmailVerified: response.data.data.setupStatus?.isEmailVerified,
                isPhoneVerified: response.data.data.setupStatus?.isPhoneVerified
              }, 'splash');

              if (!response.data.data.setupStatus?.hasProfileSetup) {
                logger.info('User has no profile setup - navigating to Name Setup', null, 'splash');
                navigationHandler.navigateFromSplash('NameSetup', userData)
              } else if (response.data.data.setupStatus?.hasPinSetup) {
                logger.info('User has PIN setup - navigating to PIN verification', null, 'splash');
                navigationHandler.navigateFromSplash('PinVerification', userData)
              } else {
                logger.info('User has no PIN setup - navigating to PIN setup', null, 'splash');
                navigationHandler.navigateFromSplash('PinSetup', userData)
              }
              return
            }
          } catch (apiError) {
            logger.warn('Token validation failed', apiError, 'splash');
            await AsyncStorage.default.removeItem('accessToken')
            await AsyncStorage.default.removeItem('refreshToken')
          }
        } else {
          logger.info('No stored access token found', null, 'splash');
        }

        logger.info('No valid authentication - navigating to startup screen', null, 'splash');
        navigationHandler.navigateFromSplash("Startup")

      } catch (error) {
        logger.error('Error during auth check', error, 'splash');
        navigationHandler.navigateFromSplash("Startup")
      }
    }

    // Wait for animations to complete, then check auth
    const timer = setTimeout(() => {
      checkAuthAndNavigate()
    }, 3000)

    return () => {
      clearTimeout(timer)
    }
  }, [])

  // Animated dots component for loading indicator
  const AnimatedDots = () => {
    const dot1Anim = useRef(new Animated.Value(0.3)).current
    const dot2Anim = useRef(new Animated.Value(0.3)).current
    const dot3Anim = useRef(new Animated.Value(0.3)).current

    useEffect(() => {
      const animateDots = () => {
        const dotSequence = (dotAnim: Animated.Value, delay: number) => {
          Animated.loop(
            Animated.sequence([
              Animated.timing(dotAnim, {
                toValue: 1,
                duration: 600,
                delay,
                useNativeDriver: true,
              }),
              Animated.timing(dotAnim, {
                toValue: 0.3,
                duration: 600,
                useNativeDriver: true,
              }),
            ])
          ).start()
        }

        dotSequence(dot1Anim, 0)
        dotSequence(dot2Anim, 200)
        dotSequence(dot3Anim, 400)
      }

      const timer = setTimeout(animateDots, 1000)
      return () => clearTimeout(timer)
    }, [])

    return (
      <View style={styles.dotsContainer}>
        <Animated.View style={[styles.dot, { opacity: dot1Anim }]} />
        <Animated.View style={[styles.dot, { opacity: dot2Anim }]} />
        <Animated.View style={[styles.dot, { opacity: dot3Anim }]} />
      </View>
    )
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: height * 0.15,
      paddingHorizontal: 40,
    },
    topSection: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      maxWidth: '100%',
    },
    logoContainer: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    logo: {
      width: Math.min(width * 0.3, 140),
      height: Math.min(width * 0.3, 140),
      resizeMode: 'contain',
    },
    bottomSection: {
      alignItems: 'center',
      minHeight: 80,
    },
    loadingContainer: {
      alignItems: 'center',
      gap: 24,
    },
    progressContainer: {
      width: Math.min(width * 0.5, 200),
      height: 2,
      backgroundColor: isDark ? 'rgba(255,255,255,0.06)' : 'rgba(0,0,0,0.06)',
      borderRadius: 1,
      overflow: 'hidden',
    },
    progressBar: {
      height: '100%',
      backgroundColor: theme.colors.primary,
      borderRadius: 1,
    },
    dotsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      gap: 8,
    },
    dot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: theme.colors.primary,
    },
  })

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar 
        barStyle={isDark ? "light-content" : "dark-content"} 
        backgroundColor="transparent" 
        translucent={true}
      />

      {/* Top Section - Logo Only */}
      <View style={styles.topSection}>
        <Animated.View
          style={[
            styles.logoContainer,
            {
              opacity: logoFadeAnim,
              transform: [
                { translateY: logoSlideAnim },
                { scale: pulseAnim }
              ],
            },
          ]}
        >
          <Animated.Image
            source={require("../../../../assets/icons/vendy.png")}
            style={styles.logo}
          />
        </Animated.View>
      </View>

      {/* Bottom Section - Loading Indicator Only */}
      <Animated.View
        style={[
          styles.bottomSection,
          {
            opacity: loadingFadeAnim,
            transform: [{ translateY: loadingSlideAnim }],
          },
        ]}
      >
        <View style={styles.loadingContainer}>
          {/* Progress Bar */}
          <View style={styles.progressContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>

          {/* Animated Dots */}
          <AnimatedDots />
        </View>
      </Animated.View>
    </SafeAreaView>
  )
}

export default SplashScreen
// ... existing code ...
