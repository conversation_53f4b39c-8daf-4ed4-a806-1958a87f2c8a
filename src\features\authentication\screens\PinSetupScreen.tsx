import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  Alert,
  Vibration,
  Animated,
  Dimensions,
  StatusBar,
  Image,
  ImageBackground,
  BackHandler,
} from 'react-native';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { useRoute, useFocusEffect, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from '../../../navigation/navigation';
import { setupService } from '../services/setupService';
import { useTheme } from '../../../shared/components/layout/ThemeContext';
import { handleAuthError } from '../services/authUtils';
import SetupLoadingScreen from './SetupLoadingScreen';
import GlassyBox from '../../../shared/components/ui/GlassyBox';
import LinearGradient from 'react-native-linear-gradient';
import { navigationHandler } from '../../../navigation/handlers/navigationHandler';
import BiometricSetupPopup from '../components/BiometricSetupPopup';
import { logger } from '../../../infrastructure/monitoring/productionLogger';


const { width } = Dimensions.get('window');

const PinSetupScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { userData } = route.params as { userData?: any };
  const { theme, isDark } = useTheme();

  const [mode, setMode] = useState<'setup' | 'verify' | 'loading'>('loading');
  const [step, setStep] = useState<'create' | 'confirm'>('create');
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [imageError, setImageError] = useState(false);
  const [mismatchedIndexes, setMismatchedIndexes] = useState<number[]>([]);
  const [hasConfirmMismatch, setHasConfirmMismatch] = useState(false);
  const [showBiometricPopup, setShowBiometricPopup] = useState(false);



  // Animation values
  const fadeAnimation = useRef(new Animated.Value(1)).current;
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  const breathingAnimation = useRef(new Animated.Value(1)).current;

  // Input refs
  const inputRefs = useRef<TextInput[]>([]);

  useEffect(() => {
    // Check if user needs PIN setup or verification
    checkPinFlowType();
  }, []);

  // Disable back navigation during PIN setup (security measure)
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Only prevent back navigation for new users in setup mode
        // Allow back for confirmation step within PIN setup
        if (mode === 'setup' && step === 'create') {
          // Show confirmation dialog for first-time PIN creation
          Alert.alert(
            'Exit Setup?',
            'You need to create a PIN to secure your account. Are you sure you want to exit?',
            [
              {
                text: 'Continue Setup',
                style: 'cancel',
              },
              {
                text: 'Exit App',
                style: 'destructive',
                onPress: () => BackHandler.exitApp(),
              },
            ]
          );
          return true; // Prevent default back action
        } else if (mode === 'setup' && step === 'confirm') {
          // Allow going back to create step
          setStep('create');
          setConfirmPin('');
          setTimeout(() => {
            inputRefs.current[0]?.focus();
          }, 100);
          return true; // Prevent default back action
        }
        
        // For verify mode, allow normal back navigation
        return false;
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [mode, step])
  );

  useEffect(() => {
    // Auto-focus first input when mode is determined
    if (mode !== 'loading') {
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    }
  }, [mode]);

  useEffect(() => {
    // Start breathing animation when loading
    if (loading) {
      const breathingLoop = Animated.loop(
        Animated.sequence([
          Animated.timing(breathingAnimation, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(breathingAnimation, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      breathingLoop.start();
      
      return () => breathingLoop.stop();
    }
  }, [loading]);

  const checkPinFlowType = async () => {
    try {
      logger.info('🔍 [PIN-SETUP] Checking PIN flow type', null, 'setup');

      const flowType = await setupService.getPinFlowType();

      logger.info('🔍 [PIN-SETUP] PIN flow type determined', {
        flowType,
        willSetMode: flowType !== 'complete'
      }, 'setup');

      if (flowType === 'complete') {
        // Setup is complete, go to main app
        logger.info('✅ [PIN-SETUP] Setup complete, navigating to main app', null, 'setup');
        navigationHandler.navigateToMainTabs();
        return;
      }

      logger.info(`🎯 [PIN-SETUP] Setting mode to: ${flowType}`, null, 'setup');
      setMode(flowType);
    } catch (error) {
      logger.error('❌ [PIN-SETUP] Error checking PIN flow type', error, 'setup');
      // Default to setup if we can't determine
      logger.info('🔄 [PIN-SETUP] Defaulting to setup mode', null, 'setup');
      setMode('setup');
    }
  };

  const handlePinChange = (value: string, index: number) => {
    const currentPin = mode === 'verify' ? pin : (step === 'create' ? pin : confirmPin);
    const newPin = currentPin.substring(0, index) + value + currentPin.substring(index + 1);
    
    // Subtle haptic feedback for each keystroke
    if (value) {
      ReactNativeHapticFeedback.trigger('impactLight', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    }
    
    if (mode === 'verify') {
      setPin(newPin);
    } else if (step === 'create') {
      setPin(newPin);
      setError(''); // Clear any previous errors immediately when typing
      setHasConfirmMismatch(false); // Reset confirm mismatch state
    } else {
      setConfirmPin(newPin);
      setError(''); // Clear any previous errors immediately when typing
      
      // Real-time PIN mismatch detection
      if (value && pin[index] && value !== pin[index]) {
        // Add this index to mismatched indexes
        setMismatchedIndexes(prev => {
          if (!prev.includes(index)) {
            return [...prev, index];
          }
          return prev;
        });
        setHasConfirmMismatch(true);
        
        // Medium haptic for mismatch
        ReactNativeHapticFeedback.trigger('impactMedium', {
          enableVibrateFallback: true,
          ignoreAndroidSystemSettings: false,
        });
      } else if (value && pin[index] && value === pin[index]) {
        // Remove this index from mismatched indexes if it now matches
        setMismatchedIndexes(prev => prev.filter(i => i !== index));
        
        // Check if all mismatches are cleared
        if (mismatchedIndexes.length === 1 && mismatchedIndexes.includes(index)) {
          setHasConfirmMismatch(false);
        }
      }
    }

    // Auto-focus next input
    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when PIN is complete
    if (newPin.length === 4) {
      if (mode === 'verify') {
        // Submit PIN for verification
        setTimeout(() => {
          handleSubmit(newPin);
        }, 300);
      } else if (step === 'create') {
        // Move to confirm step
        setTimeout(() => {
          setStep('confirm');
          setTimeout(() => {
            inputRefs.current[0]?.focus();
          }, 100);
        }, 300);
      } else {
        // Submit PIN for setup
        setTimeout(() => {
          handleSubmit(newPin);
        }, 300);
      }
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace') {
      const currentPin = mode === 'verify' ? pin : (step === 'create' ? pin : confirmPin);
      
      if (currentPin[index]) {
        // Clear current input
        const newPin = currentPin.substring(0, index) + currentPin.substring(index + 1);
        
        if (mode === 'verify') {
          setPin(newPin);
        } else if (step === 'create') {
          setPin(newPin);
        } else {
          setConfirmPin(newPin);
          // Clear mismatch state for this index when deleting
          setMismatchedIndexes(prev => prev.filter(i => i !== index));
        }
      } else if (index > 0) {
        // Move to previous input if current is empty and clear it
        const prevIndex = index - 1;
        const newPin = currentPin.substring(0, prevIndex) + currentPin.substring(prevIndex + 1);
        
        if (mode === 'verify') {
          setPin(newPin);
        } else if (step === 'create') {
          setPin(newPin);
        } else {
          setConfirmPin(newPin);
          // Clear mismatch state for previous index
          setMismatchedIndexes(prev => prev.filter(i => i !== prevIndex));
        }
        
        inputRefs.current[prevIndex]?.focus();
      }
    }
  };

  const shakeInputs = () => {
    Vibration.vibrate(400);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]).start();
  };

  const handleSubmit = async (pinToSubmit: string) => {
    if (mode === 'verify') {
      // PIN verification mode
      await handlePinVerification(pinToSubmit);
    } else {
      // PIN setup mode
      await handlePinSetup(pinToSubmit);
    }
  };

  const handlePinVerification = async (pinToSubmit: string) => {
    setLoading(true);
    setError('');

    try {
      const result = await setupService.verifyPin({ pin: pinToSubmit });
      
      // Show welcome message
      Alert.alert('Welcome Back!', result.welcomeMessage, [
        {
          text: 'Continue',
          onPress: () => {
            // Mark PIN as verified in this session
            const updatedUserData = {
              ...result.user,
              pinVerifiedInSession: true
            };
            
            if (result.setupStatus.setupComplete) {
              // Setup complete, go to main app
              navigationHandler.navigateToMainTabs();
            } else if (!result.setupStatus.hasBiometricSetup) {
              // Show biometric setup popup
              setShowBiometricPopup(true);
            } else {
              // All setup complete, go to main app
              navigationHandler.navigateToMainTabs();
            }
          }
        }
      ]);
      
    } catch (error: any) {
      console.error('PIN verification error:', error);
      
      // Check if it's an authentication error
      const authErrorHandled = await handleAuthError(error, navigation as any);
      if (authErrorHandled) {
        return;
      }
      
      setError(error.message || 'Invalid PIN. Please try again.');
      shakeInputs();
      setPin('');
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    } finally {
      setLoading(false);
    }
  };

  const handlePinSetup = async (pinToSubmit: string) => {
    if (pin !== pinToSubmit) {
      setError('PINs do not match. Please try again.');
      
      // Strong haptic for final error
      ReactNativeHapticFeedback.trigger('notificationError', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
      
      shakeInputs();
      
      // Reset state after showing error
      setTimeout(() => {
        setStep('create');
        setPin('');
        setConfirmPin('');
        setMismatchedIndexes([]);
        setHasConfirmMismatch(false);
        setError(''); // Clear the error message
        setTimeout(() => {
          inputRefs.current[0]?.focus();
        }, 100);
      }, 2000); // Show error for 2 seconds
      
      return;
    }

    setLoading(true);
    setError('');

    try {
      await setupService.setupPin({
        pin: pin,
        confirmPin: pinToSubmit,
      });

      // Show biometric setup popup after PIN setup
      setTimeout(() => {
        logger.info('🔐 [PIN-SETUP] Showing biometric setup popup', null, 'setup');
        console.log('🔐 [PIN-SETUP] Setting showBiometricPopup to true');
        setShowBiometricPopup(true);
      }, 500);
    } catch (error: any) {
      console.error('PIN setup error:', error);
      
      // Check if PIN is already set
      if (error.message === 'PIN_ALREADY_SET') {
        // Switch to verification mode
        setMode('verify');
        setStep('create');
        setPin('');
        setConfirmPin('');
        setError('');
        return;
      }
      
      // Check if it's an authentication error
      const authErrorHandled = await handleAuthError(error, navigation as any);
      if (authErrorHandled) {
        return;
      }
      
      setError(error.message || 'Failed to set PIN. Please try again.');
      shakeInputs();
      setStep('create');
      setPin('');
      setConfirmPin('');
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (mode === 'setup' && step === 'create') {
      // For new users in setup mode, show exit confirmation
      Alert.alert(
        'Exit Setup?',
        'You need to create a PIN to secure your account. Are you sure you want to exit?',
        [
          {
            text: 'Continue Setup',
            style: 'cancel',
          },
          {
            text: 'Exit App',
            style: 'destructive',
            onPress: () => BackHandler.exitApp(),
          },
        ]
      );
    } else {
      // For verify mode, allow normal back navigation
      navigationHandler.goBack();
    }
  };

  const handleBiometricSetupComplete = (enabled: boolean) => {
    setShowBiometricPopup(false);

    // Navigate to PIN verification regardless of biometric setup result
    setTimeout(() => {
      navigationHandler.resetToScreen('PinVerification', { user: userData });
    }, 300);
  };

  const renderPinInput = () => {
    const currentPin = mode === 'verify' ? pin : (step === 'create' ? pin : confirmPin);
    
    return (
      <Animated.View style={[styles.pinInputContainer, { transform: [{ translateX: shakeAnimation }] }]}>
        {[0, 1, 2, 3].map((index) => {
          const isFilled = !!currentPin[index];
          const isMismatched = (step === 'confirm' && mismatchedIndexes.includes(index)) || 
                              (error && step === 'confirm');
          
          return (
            <View key={index} style={styles.pinInputWrapper}>
              {isFilled && !isMismatched && (
                <LinearGradient
                  colors={isDark ? ['#FFFFFF', '#C084FC'] : ['#000000', '#6B21A8']}
                  style={styles.gradientGlow}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                />
              )}
              {isMismatched && (
                <LinearGradient
                  colors={['#FF6B6B', '#FF8A80']}
                  style={styles.gradientGlow}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                />
              )}
              <GlassyBox
                intensity="medium"
                glow={false}
                style={[
                  styles.glassyPinContainer,
                  isFilled && !isMismatched && styles.glassyPinContainerFilled,
                  isMismatched && styles.glassyPinContainerError,
                ] as any}
              >
                <TextInput
                  ref={(ref) => {
                    if (ref) {
                      inputRefs.current[index] = ref;
                    }
                  }}
                  style={[
                    styles.pinInput,
                    isFilled && !isMismatched && styles.pinInputFilled,
                    isMismatched && styles.pinInputError,
                  ]}
                  value={currentPin[index] || ''}
                  onChangeText={(value) => handlePinChange(value, index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  keyboardType="numeric"
                  maxLength={1}
                  secureTextEntry
                  selectTextOnFocus
                  editable={!loading}
                />
              </GlassyBox>
            </View>
          );
        })}
      </Animated.View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 24,
      paddingTop: 60,
      paddingBottom: 32,
    },
    backButton: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 3,
    },
    backButtonText: {
      fontSize: 22,
      color: theme.colors.text,
      fontWeight: '300',
    },
    progressContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    progressStep: {
      alignItems: 'center',
      gap: 6,
    },
    progressDot: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: 'transparent',
    },
    progressDotActive: {
      borderColor: 'transparent',
      shadowColor: isDark ? '#FFFFFF' : '#000000',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.4,
      shadowRadius: 12,
      elevation: 8,
    },
    progressDotText: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.muted,
    },
    progressDotTextActive: {
      color: '#FFFFFF',
      textShadowColor: isDark ? 'rgba(192, 132, 252, 0.5)' : 'rgba(107, 33, 168, 0.5)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    progressLine: {
      width: 24,
      height: 2,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
      borderRadius: 1,
    },
    progressLineActive: {
      shadowColor: isDark ? '#FFFFFF' : '#000000',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.3,
      shadowRadius: 6,
      elevation: 4,
    },
    content: {
      flex: 1,
      paddingHorizontal: 24,
      paddingTop: 16,
    },
    titleContainer: {
      alignItems: 'center',
      marginBottom: 40,
      marginTop: 8,
      paddingHorizontal: 16,
    },
    profileContainer: {
      marginBottom: 32,
      alignItems: 'center',
      marginTop: 20,
    },
    profileImage: {
      width: 100,
      height: 100,
      borderRadius: 50,
      borderWidth: 4,
      borderColor: '#007AFF',
    },
    defaultProfileContainer: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: '#007AFF',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 4,
      borderColor: '#007AFF',
    },
    defaultProfileText: {
      fontSize: 40,
      fontWeight: 'bold',
      color: 'white',
    },
    title: {
      fontSize: 32,
      fontWeight: '300',
      color: theme.colors.text,
      marginBottom: 16,
      textAlign: 'center',
      letterSpacing: -0.5,
      lineHeight: 38,
    },
    subtitle: {
      fontSize: 17,
      fontWeight: '400',
      color: theme.colors.muted,
      textAlign: 'center',
      lineHeight: 26,
      paddingHorizontal: 8,
      letterSpacing: 0.2,
    },
    pinSection: {
      alignItems: 'center',
      marginBottom: 40,
      paddingHorizontal: 16,
    },
    pinInputContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 18,
      marginBottom: 24,
      paddingVertical: 8,
    },
    pinInputWrapper: {
      position: 'relative',
    },
    gradientGlow: {
      position: 'absolute',
      top: -4,
      left: -4,
      right: -4,
      bottom: -4,
      borderRadius: 22,
      opacity: 0.6,
      zIndex: -1,
    },
    glassyPinContainer: {
      width: 60,
      height: 60,
      borderRadius: 18,
      transform: [{ scale: 1 }],
    },
    glassyPinContainerFilled: {
      transform: [{ scale: 1.05 }],
      shadowColor: theme.colors.primary,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.4,
      shadowRadius: 16,
      elevation: 20,
      borderColor: theme.colors.primary + '60',
    },
    glassyPinContainerError: {
      transform: [{ scale: 1.05 }],
      shadowColor: '#FF6B6B',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.5,
      shadowRadius: 16,
      elevation: 20,
      borderColor: '#FF6B6B',
      backgroundColor: 'rgba(255, 107, 107, 0.1)',
    },
    pinInput: {
      width: '100%',
      height: '100%',
      backgroundColor: 'transparent',
      fontSize: 24,
      fontWeight: '600',
      textAlign: 'center',
      color: theme.colors.text,
      borderWidth: 0,
      letterSpacing: 1,
      textShadowColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.15)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    pinInputFilled: {
      color: '#FFFFFF',
      fontWeight: '700',
      textShadowColor: isDark ? 'rgba(192, 132, 252, 0.6)' : 'rgba(107, 33, 168, 0.6)',
      textShadowOffset: { width: 0, height: 0 },
      textShadowRadius: 4,
      shadowColor: isDark ? '#C084FC' : '#6B21A8',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.4,
      shadowRadius: 4,
    },
    pinInputError: {
      color: '#FF6B6B',
      fontWeight: '700',
      textShadowColor: 'rgba(255, 107, 107, 0.4)',
      textShadowOffset: { width: 0, height: 0 },
      textShadowRadius: 6,
      shadowColor: '#FF6B6B',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.4,
      shadowRadius: 4,
    },
    errorText: {
      color: '#FF6B6B',
      fontSize: 15,
      fontWeight: '500',
      textAlign: 'center',
      marginTop: 16,
      paddingHorizontal: 24,
      lineHeight: 22,
      letterSpacing: 0.2,
    },
    instructionText: {
      fontSize: 14,
      fontWeight: '400',
      color: theme.colors.muted,
      textAlign: 'center',
      marginTop: 24,
      paddingHorizontal: 32,
      lineHeight: 20,
      letterSpacing: 0.1,
    },
    instructionTextWarning: {
      color: '#FF8A80',
      fontWeight: '500',
    },
    loadingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },

    loadingLogo: {
      width: 120,
      height: 120,
      resizeMode: "contain",
    },
  });

  if (mode === 'loading') {
    return <SetupLoadingScreen message="Setting up your security" />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar 
        barStyle={isDark ? "light-content" : "dark-content"} 
        backgroundColor="transparent"
        translucent={true}
      />
      
      <ImageBackground
        source={isDark ? require("../../../../assets/images/bg.jpeg") : require("../../../../assets/images/bg-white.jpeg")}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />
      
      <View style={styles.header}>
        {/* Only show back button for verify mode */}
        {mode === 'verify' ? (
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
        ) : null}
        
        {mode !== 'verify' && (
          <View style={styles.progressContainer}>
            <View style={styles.progressStep}>
              <LinearGradient
                colors={isDark ? ['#FFFFFF', '#C084FC'] : ['#000000', '#6B21A8']}
                style={[styles.progressDot, styles.progressDotActive]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Text style={[styles.progressDotText, styles.progressDotTextActive]}>1</Text>
              </LinearGradient>
            </View>
            
            <LinearGradient
              colors={step === 'confirm' 
                ? (isDark ? ['#FFFFFF', '#C084FC'] : ['#000000', '#6B21A8'])
                : ['transparent', 'transparent']
              }
              style={[styles.progressLine, step === 'confirm' && styles.progressLineActive]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            />
            
            <View style={styles.progressStep}>
              {step === 'confirm' ? (
                <LinearGradient
                  colors={isDark ? ['#FFFFFF', '#C084FC'] : ['#000000', '#6B21A8']}
                  style={[styles.progressDot, styles.progressDotActive]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={[styles.progressDotText, styles.progressDotTextActive]}>2</Text>
                </LinearGradient>
              ) : (
                <View style={styles.progressDot}>
                  <Text style={styles.progressDotText}>2</Text>
                </View>
              )}
            </View>
          </View>
        )}
      </View>

      <Animated.View style={[styles.content, { opacity: fadeAnimation }]}>
        <View style={styles.titleContainer}>
          {/* User Profile Picture - only show in verify mode */}
          {mode === 'verify' && (
            <View style={styles.profileContainer}>
              {userData?.avatar && !imageError ? (
                <Image
                  source={{ uri: userData.avatar }}
                  style={styles.profileImage}
                  onError={() => {
                    console.log('Failed to load avatar image');
                    setImageError(true);
                  }}
                />
              ) : (
                <View style={styles.defaultProfileContainer}>
                  <Text style={styles.defaultProfileText}>
                    {userData?.firstName?.charAt(0)?.toUpperCase() || 'U'}
                  </Text>
                </View>
              )}
            </View>
          )}
          
          <Text style={styles.title}>
            {mode === 'verify' 
              ? 'Welcome Back!' 
              : (step === 'create' ? 'Create Your PIN' : 'Confirm Your PIN')
            }
          </Text>
          <Text style={styles.subtitle}>
            {mode === 'verify'
              ? `Hello ${userData?.firstName || 'User'}! Enter your 4-digit PIN to continue`
              : (step === 'create' 
                ? 'Create a 4-digit PIN to secure your transactions'
                : 'Please enter your PIN again to confirm'
              )
            }
          </Text>
        </View>

        <View style={styles.pinSection}>
          {renderPinInput()}
          
          {error ? (
            <Text style={styles.errorText}>{error}</Text>
          ) : (
            <Text style={styles.instructionText}>
              {mode === 'verify'
                ? 'Enter your 4-digit PIN'
                : (step === 'create' 
                  ? 'Choose a secure 4-digit PIN'
                  : 'Confirm your PIN to continue'
                )
              }
            </Text>
          )}
        </View>
      </Animated.View>

      {loading && (
        <View style={styles.loadingOverlay}>
          <Animated.Image
            source={require("../../../../assets/icons/vendy.png")}
            style={[
              styles.loadingLogo,
              {
                transform: [{ scale: breathingAnimation }],
              },
            ]}
          />
        </View>
      )}

      {/* Biometric Setup Popup */}
      <BiometricSetupPopup
        visible={showBiometricPopup}
        onComplete={handleBiometricSetupComplete}
        userData={userData}
      />

    </SafeAreaView>
  );
};

export default PinSetupScreen;
